import React, { useCallback, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { Button, Modal } from '@/shared/components/common';
import { env } from '@/shared/utils';

interface RecaptchaModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: (token: string) => void;
  onError?: (error: string) => void;
}

/**
 * Modal component for reCAPTCHA verification
 */
const RecaptchaModal: React.FC<RecaptchaModalProps> = ({ isOpen, onClose, onSuccess, onError }) => {
  const { t } = useTranslation();
  const [recaptchaId, setRecaptchaId] = useState<number | null>(null);
  const [error, setError] = useState<string | null>(null);

  // Tải script reCAPTCHA nếu chưa có
  const loadRecaptchaScript = useCallback(() => {
    return new Promise<void>((resolve, reject) => {
      // Ki<PERSON>m tra xem script đã tồn tại chưa
      const existingScript = document.querySelector('script[src*="recaptcha/enterprise.js"]');
      if (existingScript) {
        resolve();
        return;
      }

      const script = document.createElement('script');
      script.src = 'https://www.google.com/recaptcha/enterprise.js?render=explicit';
      script.async = true;
      script.defer = true;
      script.onload = () => resolve();
      script.onerror = () => reject(new Error('Failed to load reCAPTCHA script'));
      document.head.appendChild(script);
    });
  }, []);

  // Khởi tạo reCAPTCHA khi modal được mở
  const initRecaptcha = useCallback(async () => {
    if (!isOpen) return;

    try {
      // Đảm bảo script đã được tải
      await loadRecaptchaScript();

      // Đợi một chút để script khởi tạo
      await new Promise(resolve => setTimeout(resolve, 500));

      const win = window as any;
      if (win.grecaptcha?.enterprise) {
        win.grecaptcha.enterprise.ready(() => {
          const container = document.getElementById('recaptcha-modal-container');
          if (!container) {
            setError('Container not found');
            return;
          }

          // Xóa nội dung cũ
          container.innerHTML = '';

          try {
            const id = win.grecaptcha.enterprise.render(container, {
              sitekey: env.recaptchaSiteKey,
              callback: (token: string) => {
                if (token) {
                  onSuccess(token);
                  onClose();
                }
              },
              'error-callback': () => {
                setError('reCAPTCHA verification failed');
                onError?.('reCAPTCHA verification failed');
              },
              'expired-callback': () => {
                setError('reCAPTCHA expired, please try again');
                onError?.('reCAPTCHA expired');
              },
            });
            setRecaptchaId(id);
            setError(null);
          } catch (err) {
            console.error('reCAPTCHA render error:', err);
            setError('Failed to render reCAPTCHA');
            onError?.('Failed to render reCAPTCHA');
          }
        });
      } else {
        setError('reCAPTCHA enterprise not available');
        onError?.('reCAPTCHA enterprise not available');
      }
    } catch (err) {
      console.error('reCAPTCHA init error:', err);
      setError('Failed to load reCAPTCHA script');
      onError?.('Failed to load reCAPTCHA script');
    }
  }, [isOpen, onSuccess, onClose, onError, loadRecaptchaScript]);

  // Reset reCAPTCHA
  const resetRecaptcha = useCallback(() => {
    if (recaptchaId !== null) {
      const win = window as any;
      if (win.grecaptcha?.enterprise) {
        try {
          win.grecaptcha.enterprise.reset(recaptchaId);
        } catch (err) {
          console.warn('Failed to reset reCAPTCHA:', err);
        }
      }
    }
    setRecaptchaId(null);
    setError(null);
  }, [recaptchaId]);

  // Khởi tạo reCAPTCHA khi modal mở
  useEffect(() => {
    if (isOpen) {
      const timer = setTimeout(() => {
        initRecaptcha();
      }, 300);
      return () => clearTimeout(timer);
    } else {
      resetRecaptcha();
      return undefined;
    }
  }, [isOpen, initRecaptcha, resetRecaptcha]);

  const handleClose = () => {
    resetRecaptcha();
    onClose();
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={handleClose}
      title={t('auth.recaptchaVerification', 'Xác thực reCAPTCHA')}
      size="md"
      closeOnClickOutside={false}
      closeOnEsc={true}
      footer={false}
    >
      <div className="space-y-4">
        <p className="text-sm text-gray-600 dark:text-gray-400">
          {t(
            'auth.recaptchaDescription',
            'Vui lòng hoàn thành xác thực reCAPTCHA để tiếp tục đăng nhập.'
          )}
        </p>

        {/* Container cho reCAPTCHA */}
        <div className="flex justify-center">
          <div
            id="recaptcha-modal-container"
            className="g-recaptcha flex justify-center min-h-[78px] dark:border-gray-700 rounded-md"
            data-sitekey={env.recaptchaSiteKey}
          ></div>
        </div>

        {error && <div className="text-red-500 text-sm text-center">{error}</div>}

        <div className="flex justify-end space-x-2">
          <Button variant="secondary" onClick={handleClose}>
            {t('common.cancel', 'Hủy')}
          </Button>
        </div>
      </div>
    </Modal>
  );
};

export default RecaptchaModal;
