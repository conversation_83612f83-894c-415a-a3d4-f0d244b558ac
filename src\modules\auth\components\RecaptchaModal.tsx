import React, { useEffect } from 'react';
import { useTranslation } from 'react-i18next';

import { Button, Modal } from '@/shared/components/common';
import { env } from '@/shared/utils';

import { useRecaptcha } from '../hooks/useRecaptcha';

interface RecaptchaModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: (token: string) => void;
  onError?: (error: string) => void;
}

/**
 * Modal component for reCAPTCHA verification
 */
const RecaptchaModal: React.FC<RecaptchaModalProps> = ({
  isOpen,
  onClose,
  onSuccess,
  onError,
}) => {
  const { t } = useTranslation();

  // Sử dụng hook useRecaptcha để quản lý reCAPTCHA
  const {
    recaptchaToken,
    error: recaptchaError,
    resetRecaptcha,
  } = useRecaptcha('recaptcha-modal-container', 'LOGIN');

  // Xử lý khi có token reCAPTCHA
  useEffect(() => {
    if (recaptchaToken && isOpen) {
      onSuccess(recaptchaToken);
      onClose();
    }
  }, [recaptchaToken, isOpen, onSuccess, onClose]);

  // Xử lý lỗi reCAPTCHA
  useEffect(() => {
    if (recaptchaError && isOpen) {
      onError?.(recaptchaError);
    }
  }, [recaptchaError, isOpen, onError]);

  // Reset reCAPTCHA khi đóng modal
  useEffect(() => {
    if (!isOpen) {
      resetRecaptcha();
    }
  }, [isOpen, resetRecaptcha]);

  const handleClose = () => {
    resetRecaptcha();
    onClose();
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={handleClose}
      title={t('auth.recaptchaVerification', 'Xác thực reCAPTCHA')}
      size="md"
      closeOnClickOutside={false}
      closeOnEsc={true}
    >
      <div className="space-y-4">
        <p className="text-sm text-gray-600 dark:text-gray-400">
          {t('auth.recaptchaDescription', 'Vui lòng hoàn thành xác thực reCAPTCHA để tiếp tục đăng nhập.')}
        </p>

        {/* Container cho reCAPTCHA */}
        <div className="flex justify-center">
          <div
            id="recaptcha-modal-container"
            className="g-recaptcha flex justify-center min-h-[78px] dark:border-gray-700 rounded-md"
            data-sitekey={env.recaptchaSiteKey}
          ></div>
        </div>

        {recaptchaError && (
          <div className="text-red-500 text-sm text-center">
            {recaptchaError}
          </div>
        )}

        <div className="flex justify-end space-x-2">
          <Button variant="secondary" onClick={handleClose}>
            {t('common.cancel', 'Hủy')}
          </Button>
        </div>
      </div>
    </Modal>
  );
};

export default RecaptchaModal;
